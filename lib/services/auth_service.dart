import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // TODO: Implement Google Sign-In with the new API
      // For now, show a message that it's not implemented
      debugPrint('Google Sign-In will be implemented in the next update');

      // Return null to indicate the user canceled or it's not available
      return null;
    } catch (e) {
      debugPrint('Error signing in with Google: $e');
      rethrow;
    }
  }

  /// Sign out from Firebase
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  /// Check if user profile is complete and determine navigation route
  Future<String> getNavigationRoute(User user) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .get()
          .timeout(const Duration(seconds: 20));

      final userData = snapshot.data();
      final profileCompleted = (userData?['profileCompleted'] ?? false) as bool;
      final hasUsername =
          (userData?['username']?.toString().isNotEmpty ?? false);
      final hasName = userData?['name']?.toString().isNotEmpty ?? false;
      final isEmailVerified = userData?['emailVerified'] ?? false;

      // Enhanced profile completion check
      final isActuallyComplete =
          profileCompleted || (hasUsername && hasName && isEmailVerified);

      if (isActuallyComplete) {
        return '/home';
      } else if (!hasUsername) {
        return '/choose_username';
      } else {
        return '/create_profile';
      }
    } catch (e) {
      debugPrint('Error getting navigation route: $e');
      // Default to profile creation if there's an error
      return '/create_profile';
    }
  }
}
