import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:money_mouthy_two/controllers/profile_controller.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Initialize GoogleSignIn with email scope
  // For web, the client ID is configured in web/index.html meta tag
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email'],
  );

  /// Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      GoogleSignInAccount? googleUser;

      if (kIsWeb) {
        // For web, try silent sign-in first
        googleUser = await _googleSignIn.signInSilently();
        if (googleUser == null) {
          // If silent sign-in fails, use the regular signIn method
          // Note: On web, this should ideally use renderButton, but for now we'll use signIn
          googleUser = await _googleSignIn.signIn();
        }
      } else {
        // For mobile platforms, use the regular signIn method
        googleUser = await _googleSignIn.signIn();
      }

      if (googleUser == null) {
        // User canceled the sign-in
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final UserCredential userCredential =
          await _auth.signInWithCredential(credential);
      final User? user = userCredential.user;

      if (user != null) {
        // Check if this is a new user or existing user
        final bool isNewUser =
            userCredential.additionalUserInfo?.isNewUser ?? false;

        if (isNewUser) {
          // Create user document for new Google users
          await _createGoogleUserDocument(user);
        } else {
          // Update last login for existing users
          await _updateLastLogin(user.uid);
        }

        // Initialize ProfileController
        try {
          await ProfileController.instance.initializeCurrentUser();
          debugPrint('ProfileController initialized after Google sign-in');
        } catch (e) {
          debugPrint(
              'Error initializing ProfileController after Google sign-in: $e');
        }
      }

      return userCredential;
    } catch (e) {
      debugPrint('Error signing in with Google: $e');
      // Handle specific web errors
      if (kIsWeb && e.toString().contains('popup_closed')) {
        debugPrint('User closed the Google Sign-In popup');
        return null; // Treat as user cancellation
      }
      rethrow;
    }
  }

  /// Create user document for new Google users
  Future<void> _createGoogleUserDocument(User user) async {
    try {
      await _firestore.collection('users').doc(user.uid).set({
        'name': user.displayName ?? '',
        'email': user.email ?? '',
        'emailVerified': user.emailVerified,
        'photoUrl': user.photoURL,
        'signInMethod': 'google',
        'createdAt': FieldValue.serverTimestamp(),
        'lastLogin': FieldValue.serverTimestamp(),
        'profileCompleted': false,
      }, SetOptions(merge: true));
    } catch (e) {
      debugPrint('Error creating Google user document: $e');
    }
  }

  /// Update last login timestamp
  Future<void> _updateLastLogin(String uid) async {
    try {
      await _firestore.collection('users').doc(uid).update({
        'lastLogin': FieldValue.serverTimestamp(),
        'emailVerified': true, // Google users are always email verified
      });
    } catch (e) {
      debugPrint('Error updating last login: $e');
    }
  }

  /// Sign out from both Firebase and Google
  Future<void> signOut() async {
    try {
      await Future.wait([
        _auth.signOut(),
        _googleSignIn.signOut(),
      ]);
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  /// Check if user profile is complete and determine navigation route
  Future<String> getNavigationRoute(User user) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .get()
          .timeout(const Duration(seconds: 20));

      final userData = snapshot.data();
      final profileCompleted = (userData?['profileCompleted'] ?? false) as bool;
      final hasUsername =
          (userData?['username']?.toString().isNotEmpty ?? false);
      final hasName = userData?['name']?.toString().isNotEmpty ?? false;
      final isEmailVerified = userData?['emailVerified'] ?? false;

      // Enhanced profile completion check
      final isActuallyComplete =
          profileCompleted || (hasUsername && hasName && isEmailVerified);

      if (isActuallyComplete) {
        return '/home';
      } else if (!hasUsername) {
        return '/choose_username';
      } else {
        return '/create_profile';
      }
    } catch (e) {
      debugPrint('Error getting navigation route: $e');
      // Default to profile creation if there's an error
      return '/create_profile';
    }
  }
}
